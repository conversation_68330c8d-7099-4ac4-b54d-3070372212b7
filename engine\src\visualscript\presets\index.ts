/**
 * 视觉脚本预设节点
 * 提供各种预设节点的注册
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerCoreNodes } from './CoreNodes';
import { registerMathNodes } from './MathNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerEntityNodes } from './EntityNodes';
import { registerPhysicsNodes } from './PhysicsNodes';
import { registerSoftBodyNodes } from './SoftBodyNodes';
import { registerAnimationNodes } from './AnimationNodes';
import { registerInputNodes } from './InputNodes';
import { registerAudioNodes } from './AudioNodes';
import { registerNetworkNodes } from './NetworkNodes';
import { registerHTTPNodes } from './HTTPNodes';
import { registerJSONNodes } from './JSONNodes';
import { registerDateTimeNodes } from './DateTimeNodes';
import { registerUINodes } from './UINodes';
import { registerFileSystemNodes } from './FileSystemNodes';
import { registerImageProcessingNodes } from './ImageProcessingNodes';
import { registerDatabaseNodes } from './DatabaseNodes';
import { registerCryptographyNodes } from './CryptographyNodes';
import { registerAINodes } from './AINodes';
import { registerAIModelNodes } from './AIModelNodes';
import { registerAINLPNodes } from './AINLPNodes';
import { registerAIEmotionNodes } from './AIEmotionNodes';
import { registerAIAssistantNodes } from './AIAssistantNodes';
import { registerWebRTCNodes } from './WebRTCNodes';
import { registerDebugNodes } from './DebugNodes';
import { registerTimeNodes } from './TimeNodes';
import { registerAdvancedUINodes } from './AdvancedUINodes';
import { registerAdvancedFileSystemNodes } from './AdvancedFileSystemNodes';
import { registerAdvancedImageNodes } from './AdvancedImageNodes';
import { registerAdvancedDebuggingNodes } from './AdvancedDebuggingNodes';
import { registerAsyncExampleNodes } from './AsyncExampleNodes';
import { registerFunctionExampleNodes } from './FunctionExampleNodes';
import { registerPerformanceAnalysisNodes } from './PerformanceAnalysisNodes';
import { registerPerformanceMonitoringNodes } from './PerformanceMonitoringNodes';
import { registerNetworkOptimizationNodes } from './NetworkOptimizationNodes';
import { registerNetworkProtocolNodes } from './NetworkProtocolNodes';
import { registerNetworkSecurityNodes } from './NetworkSecurityNodes';
import { registerCollaborationNodes } from './CollaborationNodes';
import { registerDistributedExecutionNodes } from './DistributedExecutionNodes';
import { registerRenderingNodes } from './RenderingNodes';
import { registerPostProcessingNodes } from './PostProcessingNodes';
import { registerSceneManagementNodes } from './SceneManagementNodes';
import { registerAssetManagementNodes } from './AssetManagementNodes';
import { registerAdvancedAnimationNodes } from './AdvancedAnimationNodes';
import { registerAdvancedUILayoutNodes } from './AdvancedUILayoutNodes';
import { registerTerrainSystemNodes } from './TerrainSystemNodes';
import { registerVegetationSystemNodes } from './VegetationSystemNodes';
import { registerBlockchainSystemNodes } from './BlockchainSystemNodes';
import { registerFluidSimulationNodes } from './FluidSimulationNodes';
import { registerMedicalSimulationNodes } from './MedicalSimulationNodes';
import { registerLearningTrackingNodes } from './LearningTrackingNodes';
import { registerMultiRegionDeploymentNodes } from './MultiRegionDeploymentNodes';

/**
 * 注册所有预设节点
 * @param registry 节点注册表
 */
export function registerAllPresetNodes(registry: NodeRegistry): void {
  // 核心节点
  registerCoreNodes(registry);
  registerMathNodes(registry);
  registerLogicNodes(registry);
  registerEntityNodes(registry);
  registerTimeNodes(registry);
  registerDebugNodes(registry);

  // 物理和动画节点
  registerPhysicsNodes(registry);
  registerSoftBodyNodes(registry);
  registerAnimationNodes(registry);
  registerAdvancedAnimationNodes(registry);
  registerFluidSimulationNodes(registry);

  // 输入输出节点
  registerInputNodes(registry);
  registerAudioNodes(registry);

  // 网络通信节点
  registerNetworkNodes(registry);
  registerHTTPNodes(registry);
  registerWebRTCNodes(registry);
  registerNetworkOptimizationNodes(registry);
  registerNetworkProtocolNodes(registry);
  registerNetworkSecurityNodes(registry);

  // UI和界面节点
  registerUINodes(registry);
  registerAdvancedUINodes(registry);
  registerAdvancedUILayoutNodes(registry);

  // 数据处理节点
  registerJSONNodes(registry);
  registerDateTimeNodes(registry);
  registerFileSystemNodes(registry);
  registerAdvancedFileSystemNodes(registry);
  registerDatabaseNodes(registry);
  registerCryptographyNodes(registry);

  // 图像处理节点
  registerImageProcessingNodes(registry);
  registerAdvancedImageNodes(registry);

  // AI和智能化节点
  registerAINodes(registry);
  registerAIModelNodes(registry);
  registerAINLPNodes(registry);
  registerAIEmotionNodes(registry);
  registerAIAssistantNodes(registry);

  // 渲染和场景节点
  registerRenderingNodes(registry);
  registerPostProcessingNodes(registry);
  registerSceneManagementNodes(registry);
  registerAssetManagementNodes(registry);

  // 专业系统节点
  registerTerrainSystemNodes(registry);
  registerVegetationSystemNodes(registry);
  registerBlockchainSystemNodes(registry);
  registerMedicalSimulationNodes(registry);
  registerLearningTrackingNodes(registry);
  registerMultiRegionDeploymentNodes(registry);

  // 协作和分布式节点
  registerCollaborationNodes(registry);
  registerDistributedExecutionNodes(registry);

  // 性能和监控节点
  registerPerformanceAnalysisNodes(registry);
  registerPerformanceMonitoringNodes(registry);
  registerAdvancedDebuggingNodes(registry);

  // 示例节点
  registerAsyncExampleNodes(registry);
  registerFunctionExampleNodes(registry);
}

// 导出所有节点注册函数
export { registerCoreNodes } from './CoreNodes';
export { registerLogicNodes } from './LogicNodes';
export { registerMathNodes } from './MathNodes';
export { registerEntityNodes } from './EntityNodes';
export { registerPhysicsNodes } from './PhysicsNodes';
export { registerSoftBodyNodes } from './SoftBodyNodes';
export { registerAnimationNodes } from './AnimationNodes';
export { registerInputNodes } from './InputNodes';
export { registerAudioNodes } from './AudioNodes';
export { registerNetworkNodes } from './NetworkNodes';
export { registerHTTPNodes } from './HTTPNodes';
export { registerJSONNodes } from './JSONNodes';
export { registerDateTimeNodes } from './DateTimeNodes';
export { registerUINodes } from './UINodes';
export { registerFileSystemNodes } from './FileSystemNodes';
export { registerImageProcessingNodes } from './ImageProcessingNodes';
export { registerDatabaseNodes } from './DatabaseNodes';
export { registerCryptographyNodes } from './CryptographyNodes';
export { registerAINodes } from './AINodes';
export { registerAIModelNodes } from './AIModelNodes';
export { registerAINLPNodes } from './AINLPNodes';
export { registerAIEmotionNodes } from './AIEmotionNodes';
export { registerAIAssistantNodes } from './AIAssistantNodes';
export { registerWebRTCNodes } from './WebRTCNodes';
export { registerDebugNodes } from './DebugNodes';
export { registerTimeNodes } from './TimeNodes';
export { registerAdvancedUINodes } from './AdvancedUINodes';
export { registerAdvancedFileSystemNodes } from './AdvancedFileSystemNodes';
export { registerAdvancedImageNodes } from './AdvancedImageNodes';
export { registerAdvancedDebuggingNodes } from './AdvancedDebuggingNodes';
export { registerAsyncExampleNodes } from './AsyncExampleNodes';
export { registerFunctionExampleNodes } from './FunctionExampleNodes';
export { registerPerformanceAnalysisNodes } from './PerformanceAnalysisNodes';
export { registerPerformanceMonitoringNodes } from './PerformanceMonitoringNodes';
export { registerNetworkOptimizationNodes } from './NetworkOptimizationNodes';
export { registerNetworkProtocolNodes } from './NetworkProtocolNodes';
export { registerNetworkSecurityNodes } from './NetworkSecurityNodes';
export { registerCollaborationNodes } from './CollaborationNodes';
export { registerDistributedExecutionNodes } from './DistributedExecutionNodes';
export { registerRenderingNodes } from './RenderingNodes';
export { registerPostProcessingNodes } from './PostProcessingNodes';
export { registerSceneManagementNodes } from './SceneManagementNodes';
export { registerAssetManagementNodes } from './AssetManagementNodes';
export { registerAdvancedAnimationNodes } from './AdvancedAnimationNodes';
export { registerAdvancedUILayoutNodes } from './AdvancedUILayoutNodes';
export { registerTerrainSystemNodes } from './TerrainSystemNodes';
export { registerVegetationSystemNodes } from './VegetationSystemNodes';
export { registerBlockchainSystemNodes } from './BlockchainSystemNodes';
export { registerFluidSimulationNodes } from './FluidSimulationNodes';
export { registerMedicalSimulationNodes } from './MedicalSimulationNodes';
export { registerLearningTrackingNodes } from './LearningTrackingNodes';
export { registerMultiRegionDeploymentNodes } from './MultiRegionDeploymentNodes';
